import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from PIL import Image

# Try to use pillow-heif (recommended), else fallback to pyheif
try:
    import pillow_heif
    pillow_heif.register_heif_opener()
    HEIF_BACKEND = "pillow-heif"
except ImportError:
    try:
        import pyheif
        HEIF_BACKEND = "pyheif"
    except ImportError:
        raise ImportError("Please install pillow-heif (recommended) or pyheif + Pillow")

def convert_heic_to_png(infile, overwrite=False):
    """Convert a single HEIC file to PNG in the same folder."""
    outfile = os.path.splitext(infile)[0] + ".png"
    if not overwrite and os.path.exists(outfile):
        return f"Skipped (exists): {outfile}"

    try:
        if HEIF_BACKEND == "pillow-heif":
            img = Image.open(infile)
        else:  # pyheif fallback
            heif_file = pyheif.read(infile)
            img = Image.frombytes(
                heif_file.mode, heif_file.size, heif_file.data, "raw", heif_file.mode
            )

        img.save(outfile, format="PNG")
        return f"Converted: {infile} → {outfile}"
    except Exception as e:
        return f"Error converting {infile}: {e}"

def main():
    heic_files = [f for f in os.listdir(".") if f.lower().endswith(".heic")]

    if not heic_files:
        print("No HEIC files found in current directory.")
        return

    print(f"Found {len(heic_files)} HEIC files. Converting...")

    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(convert_heic_to_png, f, overwrite=True) for f in heic_files]
        for f in as_completed(futures):
            print(f.result())

if __name__ == "__main__":
    main()
