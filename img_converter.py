import os
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor, as_completed
from PIL import Image
import time

# Try to use pillow-heif (recommended), else fallback to pyheif
try:
    import pillow_heif
    pillow_heif.register_heif_opener()
    HEIF_BACKEND = "pillow-heif"
except ImportError:
    try:
        import pyheif
        HEIF_BACKEND = "pyheif"
    except ImportError:
        raise ImportError("Please install pillow-heif (recommended) or pyheif + Pillow")

def convert_heic_to_png(infile, overwrite=False):
    """Convert a single HEIC file to PNG in the same folder."""
    outfile = os.path.splitext(infile)[0] + ".png"
    if not overwrite and os.path.exists(outfile):
        return f"Skipped (exists): {outfile}"

    try:
        if HEIF_BACKEND == "pillow-heif":
            img = Image.open(infile)
        else:  # pyheif fallback
            heif_file = pyheif.read(infile)
            img = Image.frombytes(
                heif_file.mode, heif_file.size, heif_file.data, "raw", heif_file.mode
            )

        img.save(outfile, format="PNG")
        return f"Converted: {infile} → {outfile}"
    except Exception as e:
        return f"Error converting {infile}: {e}"

def main():
    heic_files = [f for f in os.listdir(".") if f.lower().endswith(".heic")]

    if not heic_files:
        print("No HEIC files found in current directory.")
        return

    print(f"Found {len(heic_files)} HEIC files. Converting...")

    # Progress tracking
    completed = 0
    total = len(heic_files)
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(convert_heic_to_png, f, overwrite=True) for f in heic_files]

        for f in as_completed(futures):
            result = f.result()
            completed += 1

            # Calculate progress
            progress_percent = (completed / total) * 100
            elapsed_time = time.time() - start_time

            if completed > 0:
                avg_time_per_file = elapsed_time / completed
                estimated_total_time = avg_time_per_file * total
                remaining_time = estimated_total_time - elapsed_time

                print(f"[{completed}/{total}] ({progress_percent:.1f}%) {result}")
                if completed < total:
                    print(f"    ⏱️  Estimated time remaining: {remaining_time:.1f}s")
            else:
                print(f"[{completed}/{total}] {result}")

    total_time = time.time() - start_time
    print(f"\n✅ Conversion complete! Processed {total} files in {total_time:.1f} seconds")

if __name__ == "__main__":
    main()
